#!/usr/bin/env python3
"""
Тестовый скрипт для проверки API Nominatim для Жетысуского региона
"""

import requests
import json

def test_nominatim_search(query):
    """Тестирует поиск в Nominatim"""
    url = 'https://nominatim.openstreetmap.org/search'
    params = {
        'q': query,
        'format': 'json',
        'polygon_geojson': 1,
        'addressdetails': 1,
        'limit': 5
    }
    
    headers = {
        'User-Agent': 'KazPost-GeoService-Test/1.0'
    }
    
    print(f"\n🔍 Поиск: '{query}'")
    print(f"🌐 URL: {url}")
    print(f"📋 Параметры: {params}")
    
    try:
        response = requests.get(url, params=params, headers=headers, timeout=30)
        response.raise_for_status()
        
        data = response.json()
        
        print(f"📡 Найдено результатов: {len(data)}")
        
        for i, result in enumerate(data):
            print(f"\n--- Результат {i+1} ---")
            print(f"Название: {result.get('display_name', 'Нет названия')}")
            print(f"Тип: {result.get('type', 'Неизвестно')}")
            print(f"Класс: {result.get('class', 'Неизвестно')}")
            print(f"Важность: {result.get('importance', 'Неизвестно')}")
            print(f"Есть геометрия: {'Да' if 'geojson' in result else 'Нет'}")
            
            if 'geojson' in result:
                geom_type = result['geojson'].get('type', 'Неизвестно')
                print(f"Тип геометрии: {geom_type}")
        
        return data
        
    except Exception as e:
        print(f"❌ Ошибка: {e}")
        return None

if __name__ == "__main__":
    # Тестируем различные варианты поиска Жетысуского региона
    test_queries = [
        "Жетысуская область, Казахстан",
        "Жетысуский район, Казахстан", 
        "Жетысу, Казахстан",
        "Жетысуская, Казахстан",
        "Zhetysu, Kazakhstan",
        "Zhetysu Oblast, Kazakhstan",
        "Алматинская область, Казахстан",  # Для сравнения
    ]
    
    for query in test_queries:
        test_nominatim_search(query)
        print("\n" + "="*50)
